\documentclass[a4paper]{article}

\usepackage[latin1]{inputenc}
\usepackage[spanish]{babel}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{graphicx}
\usepackage{enumitem}

% Definir los entornos necesarios para exams
\newenvironment{question}{}{}
\newenvironment{solution}{}{}
\newenvironment{answerlist}{\begin{enumerate}}{\end{enumerate}}

\begin{document}
\SweaveOpts{concordance=TRUE}

<<echo=FALSE, results=hide>>=
# Configuracion para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")

# CONFIGURACION RADICAL ANTI-NOTACION CIENTIFICA
options(scipen = 999)
options(digits = 10)

library(exams)
library(digest)
library(testthat)

# Configuracion TikZ
typ <- match_exams_device()
if(match_exams_call() == "exams2nops") typ <- "tex"

# Semilla aleatoria
set.seed(sample(1:100000, 1))

# Funcion para formatear enteros sin notacion cientifica
formatear_entero <- function(numero) {
  formatC(as.numeric(numero), format = "d", big.mark = "")
}

# Funcion para formatear numeros monetarios (ASCII safe)
formatear_monetario <- function(numero) {
  formatear_entero(numero)
}

# Funcion principal de generacion de datos
generar_datos <- function() {
  # Precios de compra variables
  precios_compra <- c(80000, 90000, 100000, 110000, 120000, 130000, 140000, 150000)
  precio_compra <- sample(precios_compra, 1)
  
  # Precios de venta
  margen_minimo <- precio_compra + 20000
  margen_maximo <- precio_compra + 80000
  precios_venta_posibles <- seq(margen_minimo, margen_maximo, by = 10000)
  precio_venta <- sample(precios_venta_posibles, 1)
  
  # Cantidad de unidades vendidas
  cantidades <- c(5, 8, 10, 12, 15, 18, 20, 25, 30, 35, 40, 45, 50)
  cantidad_vendida <- sample(cantidades, 1)
  
  # Calcular ganancia correcta
  ganancia_unitaria <- precio_venta - precio_compra
  ganancia_total <- ganancia_unitaria * cantidad_vendida
  
  # Productos (sin acentos)
  productos_info <- list(
    list(plural = "pantalones", singular = "pantalon"),
    list(plural = "camisas", singular = "camisa"),
    list(plural = "zapatos", singular = "zapato"),
    list(plural = "chaquetas", singular = "chaqueta"),
    list(plural = "vestidos", singular = "vestido"),
    list(plural = "blusas", singular = "blusa"),
    list(plural = "faldas", singular = "falda"),
    list(plural = "shorts", singular = "short"),
    list(plural = "sueteres", singular = "sueter"),
    list(plural = "abrigos", singular = "abrigo")
  )
  producto_seleccionado <- sample(productos_info, 1)[[1]]
  producto_plural <- producto_seleccionado$plural
  producto_singular <- producto_seleccionado$singular
  
  # Nombres de comerciantes (sin acentos)
  nombres <- c("Carlos", "Maria", "Jose", "Ana", "Luis", "Carmen", "Pedro", 
              "Laura", "Miguel", "Rosa", "Antonio", "Elena", "Francisco", "Isabel")
  comerciante <- sample(nombres, 1)
  
  # Generar opciones de respuesta
  opciones <- generar_opciones_respuesta(precio_compra, precio_venta, cantidad_vendida, producto_plural)
  
  return(list(
    precio_compra = precio_compra,
    precio_venta = precio_venta,
    cantidad_vendida = cantidad_vendida,
    ganancia_unitaria = ganancia_unitaria,
    ganancia_total = ganancia_total,
    producto_plural = producto_plural,
    producto_singular = producto_singular,
    comerciante = comerciante,
    opciones = opciones$opciones,
    explicaciones = opciones$explicaciones,
    solutions = opciones$solutions
  ))
}

# Funcion para generar opciones de respuesta
generar_opciones_respuesta <- function(precio_compra, precio_venta, cantidad, producto) {
  
  # Opcion A: CORRECTA
  opcion_a <- paste0("Restar ", formatear_monetario(precio_compra), 
                    " al precio de venta de cada ", producto, 
                    " y multiplicar dicho valor por el numero de unidades vendidas.")
  
  # Opcion B: INCORRECTA - Orden invertido
  opcion_b <- paste0("A ", formatear_monetario(precio_compra), 
                    " restarle el precio de venta de cada ", producto, 
                    " y multiplicar dicho valor por el numero de unidades vendidas.")
  
  # Opcion C: INCORRECTA - Operaciones mal ordenadas
  opcion_c <- paste0("Restar el resultado de la multiplicacion entre ", 
                    formatear_monetario(precio_compra), 
                    " y el numero de unidades vendidas, al precio de venta de cada ", producto, ".")
  
  # Opcion D: INCORRECTA - Logica invertida
  opcion_d <- paste0("Restar el resultado de la multiplicacion entre el precio de venta de cada ", 
                    producto, " y el numero de unidades vendidas, a ", 
                    formatear_monetario(precio_compra), ".")
  
  opciones <- c(opcion_a, opcion_b, opcion_c, opcion_d)
  
  # Explicaciones para cada opcion
  explicaciones <- c(
    "Verdadero. Este es el procedimiento correcto: (Precio venta - Precio compra) x Cantidad = Ganancia total.",
    "Falso. Este procedimiento invierte el orden de la resta, resultando en un valor negativo incorrecto.",
    "Falso. Este procedimiento calcula mal las operaciones, no representa la ganancia real.",
    "Falso. Este procedimiento tiene la logica completamente invertida y no calcula ganancias."
  )
  
  # Vector de soluciones
  solutions <- c(TRUE, FALSE, FALSE, FALSE)
  
  return(list(
    opciones = opciones,
    explicaciones = explicaciones,
    solutions = solutions
  ))
}

# Generar datos para este ejercicio
datos <- generar_datos()
@

<<echo=FALSE, results=hide>>=
# Prueba de diversidad de versiones
test_that("Prueba de diversidad de versiones", {
  versiones <- list()
  for(i in 1:1000) {
    datos_test <- generar_datos()
    version_string <- paste(datos_test$precio_compra, datos_test$precio_venta, 
                           datos_test$cantidad_vendida, datos_test$producto_plural, 
                           datos_test$comerciante, collapse = "-")
    versiones[[i]] <- digest::digest(version_string)
  }
  
  n_versiones_unicas <- length(unique(versiones))
  expect_true(n_versiones_unicas >= 300,
              info = paste("Solo se generaron", n_versiones_unicas,
                          "versiones unicas. Se requieren al menos 300."))
})
@

\begin{question}

\Sexpr{datos$comerciante} es un comerciante que desea conocer el valor de las ganancias que obtiene por la venta de \Sexpr{datos$producto_plural}. En este sentido, si el comerciante compra cada \Sexpr{datos$producto_singular} a \Sexpr{formatear_monetario(datos$precio_compra)} pesos, cual de los siguientes procedimientos le permite determinar el monto de sus ganancias?

<<echo=FALSE, results=tex>>=
answerlist(datos$opciones)
@

\end{question}

\begin{solution}

Para calcular las ganancias de un comerciante, se debe aplicar la formula:

\textbf{Ganancia = (Precio de venta - Precio de compra) x Numero de unidades vendidas}

En este caso:
\begin{itemize}
\item Precio de compra por unidad: \Sexpr{formatear_monetario(datos$precio_compra)} pesos
\item Si vende \Sexpr{datos$cantidad_vendida} unidades, la ganancia sera: (Precio venta - \Sexpr{formatear_monetario(datos$precio_compra)}) x \Sexpr{datos$cantidad_vendida}
\end{itemize}

El procedimiento correcto es \textbf{restar el precio de compra al precio de venta} y luego \textbf{multiplicar por la cantidad vendida}.

<<echo=FALSE, results=tex>>=
answerlist(datos$explicaciones)
@

\end{solution}

%% META-INFORMATION
\exname{Ganancias Comerciante ASCII Safe}
\extype{schoice}
\exsolution{\Sexpr{mchoice2string(datos$solutions)}}
\exshuffle{TRUE}
\exsection{Formulacion Ejecucion Numerico Variacional}

\end{document}
