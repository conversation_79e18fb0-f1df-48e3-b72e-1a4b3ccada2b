\documentclass[a4paper]{article}

\usepackage[utf8]{inputenc}
\usepackage[spanish]{babel}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{graphicx}
\usepackage{enumitem}

% Definir los entornos necesarios para exams
\newenvironment{question}{}{}
\newenvironment{solution}{}{}
\newenvironment{answerlist}{\begin{enumerate}}{\end{enumerate}}

\begin{document}
\SweaveOpts{concordance=TRUE}

<<echo=FALSE, results=hide>>=
library(exams)
options(scipen = 999)

# Datos fijos para prueba
precio_compra <- 100000
precio_venta <- 150000
cantidad <- 10
ganancia <- (precio_venta - precio_compra) * cantidad

# Opciones de respuesta
opciones <- c(
  "Restar 100000 al precio de venta y multiplicar por cantidad",
  "A 100000 restarle el precio de venta y multiplicar por cantidad", 
  "Restar resultado de multiplicacion entre 100000 y cantidad al precio de venta",
  "Restar resultado de multiplicacion entre precio de venta y cantidad a 100000"
)

explicaciones <- c(
  "Verdadero. Procedimiento correcto para calcular ganancias.",
  "Falso. Orden invertido de la resta.",
  "Falso. Operaciones mal ordenadas.",
  "Falso. Logica completamente invertida."
)

solutions <- c(TRUE, FALSE, FALSE, FALSE)
@

\begin{question}

Carlos es un comerciante que compra pantalones a 100000 pesos cada uno. Cual procedimiento le permite determinar sus ganancias?

<<echo=FALSE, results=tex>>=
answerlist(opciones)
@

\end{question}

\begin{solution}

Para calcular ganancias: Ganancia = (Precio venta - Precio compra) × Cantidad

<<echo=FALSE, results=tex>>=
answerlist(explicaciones)
@

\end{solution}

%% META-INFORMATION
\exname{Test Ganancias Simple}
\extype{schoice}
\exsolution{\Sexpr{mchoice2string(solutions)}}
\exshuffle{TRUE}

\end{document}
